#!/usr/bin/env python3
"""
MCP服务测试脚本
测试MCP服务的基本功能和响应格式
"""
import requests
import json

def test_mcp_service():
    """测试MCP服务"""
    base_url = "http://localhost:8000"
    
    print("🧪 开始测试MCP服务...")
    
    # 1. 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health_check")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False
    
    # 2. 测试MCP端点
    print("\n2. 测试MCP端点...")
    try:
        response = requests.get(f"{base_url}/mcp")
        print(f"状态码: {response.status_code}")
        mcp_info = response.json()
        print(f"MCP信息: {json.dumps(mcp_info, indent=2, ensure_ascii=False)}")
        
        # 检查可用工具
        if "available_tools" in mcp_info:
            print(f"可用工具数量: {len(mcp_info['available_tools'])}")
            for tool in mcp_info['available_tools']:
                print(f"  - {tool}")
    except Exception as e:
        print(f"MCP端点测试失败: {e}")
        return False
    
    # 3. 测试证书域功能
    print("\n3. 测试证书域功能...")
    try:
        # 测试获取测试账号
        response = requests.post(f"{base_url}/api/v1/certificate/get_test_account")
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 检查响应是否包含请求详情
        if "request_details" in result:
            print("✅ 响应包含请求详情")
            print(f"请求URL: {result['request_details'].get('url', 'N/A')}")
            print(f"请求方法: {result['request_details'].get('method', 'N/A')}")
        else:
            print("❌ 响应缺少请求详情")
            
        if "response_details" in result:
            print("✅ 响应包含响应详情")
            print(f"状态码: {result['response_details'].get('status_code', 'N/A')}")
        else:
            print("❌ 响应缺少响应详情")
            
    except Exception as e:
        print(f"证书域功能测试失败: {e}")
        return False
    
    print("\n✅ MCP服务测试完成！")
    return True

if __name__ == "__main__":
    success = test_mcp_service()
    if success:
        print("\n🎉 所有测试通过！MCP服务运行正常。")
    else:
        print("\n❌ 测试失败，请检查服务配置。")
