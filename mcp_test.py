#!/usr/bin/env python3
"""
MCP服务测试脚本 - 验证Cursor可以正确配置和使用
"""
import requests
import json

def test_mcp_endpoints():
    """测试MCP端点"""
    base_url = "http://localhost:8002"
    
    print("🧪 测试MCP服务端点...")
    
    # 1. 测试健康检查
    print("\n1. 健康检查:")
    try:
        response = requests.get(f"{base_url}/health_check")
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ 响应: {response.json()}")
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False
    
    # 2. 测试MCP端点 (SSE)
    print("\n2. MCP端点 (SSE):")
    try:
        response = requests.get(f"{base_url}/mcp", stream=True, timeout=5)
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        # 读取前几行SSE数据
        lines = []
        for line in response.iter_lines(decode_unicode=True):
            if line:
                lines.append(line)
                if len(lines) >= 3:  # 只读取前3行
                    break
        
        print("✅ SSE响应前几行:")
        for line in lines:
            print(f"   {line}")
            
    except Exception as e:
        print(f"❌ MCP端点测试失败: {e}")
    
    # 3. 测试API文档
    print("\n3. API文档:")
    try:
        response = requests.get(f"{base_url}/docs")
        print(f"✅ 状态码: {response.status_code}")
        print(f"✅ API文档可访问")
    except Exception as e:
        print(f"❌ API文档访问失败: {e}")
    
    # 4. 测试OpenAPI规范
    print("\n4. OpenAPI规范:")
    try:
        response = requests.get(f"{base_url}/api/v1/openapi.json")
        print(f"✅ 状态码: {response.status_code}")
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get("paths", {})
            print(f"✅ 可用端点数量: {len(paths)}")
            
            # 统计各业务域的端点
            domains = {}
            for path in paths.keys():
                if "/certificate/" in path:
                    domains["证书域"] = domains.get("证书域", 0) + 1
                elif "/signing/" in path:
                    domains["签署域"] = domains.get("签署域", 0) + 1
                elif "/saas/" in path:
                    domains["SaaS域"] = domains.get("SaaS域", 0) + 1
                elif "/identity/" in path:
                    domains["实名域"] = domains.get("实名域", 0) + 1
                elif "/intention/" in path:
                    domains["意愿域"] = domains.get("意愿域", 0) + 1
                elif "/platform/" in path:
                    domains["平台功能"] = domains.get("平台功能", 0) + 1
            
            print("✅ 业务域分布:")
            for domain, count in domains.items():
                print(f"   {domain}: {count}个端点")
                
    except Exception as e:
        print(f"❌ OpenAPI规范获取失败: {e}")
    
    print("\n🎉 MCP服务测试完成！")
    print(f"\n📋 Cursor MCP配置信息:")
    print(f"   MCP服务地址: {base_url}/mcp")
    print(f"   协议类型: SSE (Server-Sent Events)")
    print(f"   健康检查: {base_url}/health_check")
    print(f"   API文档: {base_url}/docs")
    
    return True

if __name__ == "__main__":
    test_mcp_endpoints()
