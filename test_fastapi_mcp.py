#!/usr/bin/env python3
"""
测试FastApiMCP的基本使用方式
"""
from fastapi import FastAPI
from fastapi_mcp import FastApiMCP

# 创建一个简单的FastAPI应用
app = FastAPI(title="Test MCP App")

@app.get("/test", operation_id="test_endpoint")
async def test_endpoint():
    """测试端点"""
    return {"message": "Hello from test endpoint"}

@app.post("/echo", operation_id="echo_message")
async def echo_message(message: str):
    """回显消息"""
    return {"echo": message}

# 创建MCP服务
mcp = FastApiMCP(app)

# 挂载MCP服务
mcp.mount()

if __name__ == "__main__":
    import uvicorn
    print("启动测试MCP服务...")
    print("MCP端点应该在: http://localhost:8001/mcp")
    uvicorn.run(app, host="0.0.0.0", port=8001)
