 模拟环境配置文件
environment: mock
description: "模拟环境配置 - 用于模拟测试场景"

# API服务地址配置
api_urls:
  sdk: "http://mock-sdk.tsign.cn"
  cert: "http://mock-cert.tsign.cn"
  footstone: "http://mock-openapi.tsign.cn"

# 应用ID配置
app_ids:
  default: "mock_app_id"
  cert: "mock_cert_id"
  signing: "mock_signing_id"

# 默认请求头配置
headers:
  Content-Type: "application/json"
  X-Tsign-Open-Auth-Mode: "mock"
  X-Tsign-Service-Group: "MOCK"

# 业务域配置
business_domains:
  signing:
    name: "签署域"
    description: "电子签名相关功能"
    app_id: "mock_signing_id"
    base_url: "http://mock-sdk.tsign.cn"
    
  saas:
    name: "SaaS域"
    description: "SaaS平台相关功能"
    app_id: "mock_app_id"
    base_url: "http://mock-sdk.tsign.cn"
    
  identity:
    name: "实名域"
    description: "实名认证相关功能"
    app_id: "mock_app_id"
    base_url: "http://mock-openapi.tsign.cn"
    
  intention:
    name: "意愿域"
    description: "签署意愿相关功能"
    app_id: "mock_app_id"
    base_url: "http://mock-sdk.tsign.cn"
    
  certificate:
    name: "证书域"
    description: "数字证书相关功能"
    app_id: "mock_cert_id"
    base_url: "http://mock-cert.tsign.cn"

# 请求配置
request_config:
  timeout: 10
  retry_times: 1
  log_requests: true
